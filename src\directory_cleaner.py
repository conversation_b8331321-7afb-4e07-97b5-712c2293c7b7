#!/usr/bin/env python3
"""
Directory Cleaner - Removes empty directories and specified files.

Usage:
  python directory_cleaner.py [PATH] [--dry-run] [--yes] [--prompt]

Examples:
  python directory_cleaner.py                   # Clean current directory (with prompts)
  python directory_cleaner.py /some/path        # Clean /some/path (with prompts)
  python directory_cleaner.py --dry-run         # Simulate removals without deleting
  python directory_cleaner.py --yes             # Remove without confirming each directory
  python directory_cleaner.py --prompt          # Enter interactive mode for directory & flags
"""

import os
import sys
import argparse
from pathlib import Path

# =============================================================================
# CONSTANTS - Files to delete during cleanup
# =============================================================================
FILES_TO_DELETE = [
    "youtube_downloader.log",
    "debug.log",
    "error.log",
    "temp.txt",
    "cache.tmp",
    ".DS_Store",
    "Thumbs.db",
    "desktop.ini"
]

def parse_arguments():
    parser = argparse.ArgumentParser(
        description="Remove empty directories and specified files from a specified path."
    )
    parser.add_argument(
        "path",
        nargs="?",
        default=None,
        help="Path to the directory you wish to clean."
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Simulate the removal process (no directories will actually be deleted)."
    )
    parser.add_argument(
        "-y", "--yes",
        default=True,
        action="store_true",
        help="Automatically confirm all deletions without prompting."
    )
    parser.add_argument(
        "--prompt",
        action="store_true",
        help="Enter interactive mode to specify path and settings."
    )
    return parser.parse_args()

def prompt_bool(question: str, default: bool = False) -> bool:
    """
    Interactive boolean prompt. Returns True if user types 'y', otherwise False.
    Uses [Y/n] or [y/N] to indicate the default.
    """
    hint = "[Y/n]" if default else "[y/N]"
    while True:
        answer = input(f"{question} {hint}: ").strip().lower()
        if not answer:
            return default
        if answer in ["y", "yes"]:
            return True
        if answer in ["n", "no"]:
            return False
        print("Please enter y or n.")

def confirm(message: str, auto_confirm: bool) -> bool:
    """
    Ask the user for confirmation unless auto-confirm is enabled.
    Returns True if confirmed, otherwise False.
    """
    if auto_confirm:
        return True
    answer = input(f"{message} [y/N]: ").strip().lower()
    return (answer == "y")

def remove_specified_files(target: Path, dry_run: bool, auto_confirm: bool) -> tuple[int, int]:
    """
    Traverse the directory and remove files that match the FILES_TO_DELETE list.
    Returns a tuple of (matched_count, removed_count).
    """
    matched, removed = 0, 0

    for root, dirs, files in os.walk(target):
        for file_name in files:
            if file_name in FILES_TO_DELETE:
                file_path = Path(root) / file_name
                matched += 1
                if dry_run:
                    print(f"[DRY RUN] Found file to delete: {file_path}")
                else:
                    # Prompt for confirmation (unless --yes was given)
                    if confirm(f"Delete file: {file_path}?", auto_confirm):
                        try:
                            file_path.unlink()
                            removed += 1
                            print(f"Removed: {file_path}")
                        except Exception as e:
                            print(f"Error removing {file_path}: {e}", file=sys.stderr)
                    else:
                        print(f"Skipped: {file_path}")

    return matched, removed


def remove_empty_directories(target: Path, dry_run: bool, auto_confirm: bool) -> tuple[int, int]:
    """
    Traverse the directory bottom-up, detect empty folders, and remove them
    if confirmed. Returns a tuple of (matched_count, removed_count).
    """
    matched, removed = 0, 0

    # Bottom-up ensures we remove empties in nested folders first
    for root, dirs, files in os.walk(target, topdown=False):
        path_obj = Path(root)
        try:
            # If directory is empty
            if not any(path_obj.iterdir()):
                matched += 1
                if dry_run:
                    print(f"[DRY RUN] Found empty directory: {path_obj}")
                else:
                    # Prompt for confirmation (unless --yes was given)
                    if confirm(f"Delete empty directory: {path_obj}?", auto_confirm):
                        path_obj.rmdir()
                        removed += 1
                        print(f"Removed: {path_obj}")
                    else:
                        print(f"Skipped: {path_obj}")
        except Exception as e:
            print(f"Error accessing {path_obj}: {e}", file=sys.stderr)

    return matched, removed

def interactive_config(args) -> argparse.Namespace:
    """
    Interactive configuration: Ask the user for path, whether to run dry-run,
    and whether to auto-confirm deletions.
    """
    print("\n--- Interactive Directory Cleaner Setup ---")
    print(f"Files to be deleted: {', '.join(FILES_TO_DELETE)}")
    print()

    # Prompt for target path
    default_path = args.path if args.path else "."
    user_path = input(f"Directory to clean [{default_path}]: ").strip()
    if not user_path:
        user_path = default_path

    # Prompt for dry-run
    user_dry_run = prompt_bool("Run in dry-run mode? (no actual deletion)", default=args.dry_run)

    # Prompt for auto-confirm
    user_yes = prompt_bool("Auto-confirm deletions? (no per-file/directory prompt)", default=args.yes)

    # Update and return the revised args
    new_args = argparse.Namespace(
        path=user_path,
        dry_run=user_dry_run,
        yes=user_yes,
        prompt=args.prompt
    )
    print("\n-----------------------------------------\n")
    return new_args

def main():
    args = parse_arguments()

    # If --prompt, ask user interactively for the path/dry-run/yes settings
    if args.prompt:
        args = interactive_config(args)

    # Use the final path value, defaulting to current directory (".") if still None
    target_path = Path(args.path or ".")

    # Validate target path
    if not target_path.is_dir():
        print(f"Error: '{target_path}' is not a directory or does not exist.", file=sys.stderr)
        sys.exit(1)

    print(f"Scanning directory: {target_path}")
    print(f"Files to be deleted: {', '.join(FILES_TO_DELETE)}")
    if args.dry_run:
        print("Running in DRY RUN mode. No files or directories will be deleted.")
    if args.yes:
        print("Auto-confirm mode enabled. Deletions will proceed without prompting.")
    print()

    # Remove specified files first
    files_matched, files_removed = remove_specified_files(target_path, args.dry_run, args.yes)

    # Then remove empty directories
    dirs_matched, dirs_removed = remove_empty_directories(target_path, args.dry_run, args.yes)

    print("\nSummary")
    print(f"  Files found for deletion: {files_matched}")
    print(f"  Empty directories found: {dirs_matched}")
    if args.dry_run:
        print("  Removals simulated (DRY RUN). No actual changes were made.")
    else:
        print(f"  Files removed: {files_removed}")
        print(f"  Empty directories removed: {dirs_removed}")

    # Wait for user to press Enter before exiting
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
